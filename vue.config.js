const path = require('path');
const webpack = require('webpack');
const LodashModuleReplacementPlugin = require('lodash-webpack-plugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin');
// const ImageminPlugin = require('imagemin-webpack-plugin').default;
// const CopyWebpackPlugin = require('copy-webpack-plugin');

// 检测是否为生产环境
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

let env = process.env.VUE_APP_NODE_ENV;
if(env === 'production'){
  env = 'prod';
}
const config = require(`./src/config/index.${env}.js`);
const TerserPlugin = require('terser-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const resolve = dir => {
  return path.join(__dirname, dir);
};

// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
// const BASE_URL = process.env.VUE_APP_CMD === 'build' ? config.CdnDomain : '/';
const BASE_URL =  '/'

module.exports = {
  lintOnSave: false,
  // Project deployment base
  // By default we assume your app will be deployed at the root of a domain,
  // e.g. https://www.my-app.com/
  // If your app is deployed at a sub-path, you will need to specify that
  // sub-path here. For example, if your app is deployed at
  // https://www.foobar.com/my-app/
  // then change this productionSourceMapto '/my-app/'
  publicPath: BASE_URL,
  // tweak internal webpack configuration.
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  // 如果你不需要使用eslint，把lintOnSave设为false即可

  chainWebpack: config => {
    // svg rule loader
    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
        logLevel: 'silent'
      })
      .end();

      config.module
      .rule('langfuse-fix')
      .test(/\.m?js$/)
      .include.add(/node_modules[\\/]langfuse/)
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .options({
        presets: ['@babel/preset-env'],
        sourceType: 'unambiguous',
        plugins: ['@babel/plugin-proposal-numeric-separator'],
      })


    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('_c', resolve('src/components'))
      .set('libs', resolve('src/libs'));

    // 生产环境优化
    if (isProduction) {
      // 生产环境使用thread-loader加速构建
      const cpuCount = require('os').cpus().length;
      if (cpuCount > 2) {
        ['js', 'vue'].forEach(rule => {
          config.module
            .rule(rule)
            .use('thread-loader')
            .loader('thread-loader')
            .options({
              workers: Math.max(2, Math.min(cpuCount - 1, 4)), // 限制最大worker数量
              poolTimeout: 2000,
              poolParallelJobs: 50,
              name: `${rule}-pool`
            })
            .before('babel-loader');
        });
      }

      // 优化模块解析
      config.resolve.modules
        .clear()
        .add('node_modules')
        .add(resolve('src'))
        .add(resolve('node_modules'));

      // 减少文件系统调用
      config.resolve.extensions
        .clear()
        .add('.js')
        .add('.vue')
        .add('.json');

      // 优化babel-loader
      config.module
        .rule('js')
        .use('babel-loader')
        .tap(options => ({
          ...options,
          cacheDirectory: true,
          cacheCompression: false,
          compact: true,
        }));
    }

    // 开发环境优化
    if (isDevelopment) {
      // 开发环境启用缓存
      config.cache(true);

      // 开发环境使用thread-loader加速构建（仅对大项目有效）
      const cpuCount = require('os').cpus().length;
      if (cpuCount > 2) {
        config.module
          .rule('js')
          .test(/\.js$/)
          .use('thread-loader')
          .loader('thread-loader')
          .options({
            workers: Math.max(1, cpuCount - 1),
            poolTimeout: isDevelopment ? Infinity : 2000,
          });
      }
    }

    // 性能监控插件（仅在需要时启用）
    if (process.env.ANALYZE_BUILD) {
      const smp = new SpeedMeasurePlugin({
        outputFormat: 'human',
        compareLoadersBuild: {
          filePath: './build-speed-analysis.json'
        }
      });
      config.plugin('speed-measure-webpack-plugin').use(smp).end();
    }

    // Bundle分析插件
    if (process.env.ANALYZE_BUNDLE) {
      const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
      config.plugin('bundle-analyzer').use(BundleAnalyzerPlugin, [{
        analyzerMode: 'static',
        openAnalyzer: false,
        reportFilename: 'bundle-analysis.html'
      }]);
    }

    // 删除prefetch和preload以减少初始加载
    config.plugins.delete('prefetch');
    config.plugins.delete('preload');

    // HTML插件配置
    config.plugin('html').tap(args => {
      args[0].title = '榕树家中医智慧诊疗平台';
      // 生产环境启用压缩
      if (isProduction) {
        args[0].minify = {
          removeComments: true,
          collapseWhitespace: true,
          removeAttributeQuotes: true,
          collapseBooleanAttributes: true,
          removeScriptTypeAttributes: true,
        };
      }
      return args;
    });
    // Vue loader配置优化
    config.module.rule('vue').use('vue-loader').loader('vue-loader').tap(options => {
      options.compilerOptions = {
        preserveWhitespace: false,
        sourceMap: isProduction && process.env.VUE_APP_CMD === 'build',
      };
      options.compiler = require('vue-template-babel-compiler');
      return options;
    });
    // config.plugin('uselessFile')
    //   .use(
    //     new UselessFile({
    //       root: path.resolve(__dirname,'./src/assets/images'),
    //       clean:true,
    //       exclude: /node_modules/
    //     })
    //   )
    // or:
    // modify its options:
    // config.plugin('prefetch').tap(options => {
    //   options[0].fileBlacklist = options[0].fileBlacklist || []
    //   options[0].fileBlacklist.push(/myasyncRoute(.)+?\.js$/)
    //   return options
    // })
  },
  // webpack打包配置
  configureWebpack: config => {
    // 简化构建日志输出
    config.stats = {
      colors: true,
      modules: false,
      children: false,
      chunks: false,
      chunkModules: false,
      entrypoints: false,
      warnings: true,
      assets: false,
      version: false,
      hash: false,
      timings: false,
      builtAt: false,
    };

    if (process.env.VUE_APP_NODE_ENV === 'production' && process.env.VUE_APP_CMD === 'build') {
      config.plugins.push(
        sentryWebpackPlugin({
          project: 'fpc-clinic',
          org: 'sentry',
          url: 'https://armssy.rsjxx.com',
          authToken: process.env.SENTRY_AUTH_TOKEN,
          sourcemaps: {
            ignore: ['node_modules', 'vue.config.js'],
            // filesToDeleteAfterUpload: true,
          },
          release: {
            name: 'v1.0.2',
            cleanArtifacts: true,
          },
          telemetry: true,
        })
      );
    }
    if (process.env.NODE_ENV === 'production') {
      //     //为生产环境修改配置。。。
      config.mode = 'production';
      const productionGzipExtensions = ['html', 'js', 'css'];

      // 优化moment.js只加载中文语言包
      config.plugins.push(new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn/));

      // webpack 5 内置缓存配置 - 持久化缓存
      config.cache = {
        type: 'filesystem',
        cacheDirectory: path.join(__dirname, './node_modules/.cache/webpack'),
        // 缓存版本，当需要强制清除缓存时可以修改这个值
        version: '1.0.2',
        buildDependencies: {
          config: [__filename],
          // 当这些文件变化时，缓存失效
          package: [path.resolve(__dirname, 'package.json')],
          babel: [path.resolve(__dirname, 'babel.config.js')],
        },
        // 缓存压缩 - 生产环境启用压缩，开发环境关闭以提升速度
        compression: false,
        // 缓存存储策略
        store: 'pack',
        // 缓存名称
        name: `${process.env.NODE_ENV}-${process.env.VUE_APP_NODE_ENV}`,
        // 缓存管理策略
        managedPaths: [path.resolve(__dirname, 'node_modules')],
        // 缓存最大年龄
        maxAge: 1000 * 60 * 60 * 24 * 7, // 7天
      };

      // 启用LodashModuleReplacementPlugin减少lodash体积
      config.plugins.push(
        new LodashModuleReplacementPlugin()
      );

      // 图片压缩优化 - 暂时禁用，因为 imagemin-webpack-plugin 与 webpack 5 不兼容
      // TODO: 替换为 webpack 5 兼容的图片压缩方案
      // config.plugins.push(
      //   new ImageminPlugin({
      //     test: /\.(jpe?g|png|gif|svg)$/i,
      //     pngquant: {
      //       quality: '60-80'
      //     },
      //     optipng: {
      //       optimizationLevel: 7
      //     },
      //     gifsicle: {
      //       optimizationLevel: 3
      //     },
      //     svgo: {
      //       plugins: [
      //         { name: 'removeViewBox', active: false },
      //         { name: 'removeEmptyAttrs', active: false }
      //       ]
      //     }
      //   })
      // );
      // gzip 压缩插件 - 已验证与 webpack 5 兼容
      config.plugins.push(
        new CompressionWebpackPlugin({
          filename: '[path][base].gz',
          algorithm: 'gzip',
          test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
          threshold: 10240, // 只有大小大于该值的资源会被处理 10240
          minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
          deleteOriginalAssets: false // 删除原文件
        })
      );
      let optimization = {
        // 运行时代码单独提取
        runtimeChunk: 'single',
        // 启用 webpack 5 的新优化特性
        realContentHash: true,
        // 模块 ID 优化
        moduleIds: 'deterministic',
        chunkIds: 'deterministic',
        // 移除空的 chunks
        removeEmptyChunks: true,
        // 合并重复的 chunks
        mergeDuplicateChunks: true,
        // 移除可用的模块
        removeAvailableModules: true,
        // 标记副作用 - 修复 Reflect API 问题
        sideEffects: false,
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          maxSize: 300000,
          minChunks: 1,
          maxAsyncRequests: 20, // 减少异步请求数量
          maxInitialRequests: 20, // 减少初始请求数量
          cacheGroups: {
            // 默认组
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true
            },
            // 核心第三方库 - 高优先级，小体积，常用
            coreVendors: {
              name: 'vendors-core',
              test: /[\\/]node_modules[\\/](vue|vue-router|vuex|axios|core-js)[\\/]/,
              priority: 40,
              chunks: 'all',
              enforce: true
            },
            // UI 库 - element-ui 单独打包
            elementUI: {
              name: 'element-ui',
              test: /[\\/]node_modules[\\/]element-ui[\\/]/,
              priority: 35,
              chunks: 'all',
              enforce: true
            },
            // view-design 单独打包
            viewDesign: {
              name: 'view-design',
              test: /[\\/]node_modules[\\/]view-design[\\/]/,
              priority: 35,
              chunks: 'all',
              enforce: true
            },
            // 图表库 - echarts 单独打包
            echarts: {
              name: 'echarts',
              test: /[\\/]node_modules[\\/]echarts[\\/]/,
              priority: 30,
              chunks: 'all',
              enforce: true
            },
            // 工具库 - lodash-es, moment 等
            utils: {
              name: 'vendors-utils',
              test: /[\\/]node_modules[\\/](lodash-es|moment|dayjs|crypto-js)[\\/]/,
              priority: 25,
              chunks: 'all'
            },
            // 大型库 - tinymce, xlsx 等
            largeLibs: {
              name: 'vendors-large',
              test: /[\\/]node_modules[\\/](@?tinymce|xlsx|d3)[\\/]/,
              priority: 25,
              chunks: 'all'
            },
            // 其他第三方库
            vendors: {
              name: 'vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'all',
              minSize: 30000,
              maxSize: 200000
            },
            // 公共业务代码
            common: {
              name: 'common',
              minChunks: 2,
              priority: 5,
              chunks: 'all',
              reuseExistingChunk: true,
              minSize: 10000
            }
          }
        },
        // splitChunks: {
        //   cacheGroups: {
        //     vendor: {
        //       chunks: 'all',
        //       test: /node_modules/,
        //       name: 'vendor',
        //       minChunks: 1,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 100
        //     },
        //     common: {
        //       chunks: 'all',
        //       test: /[\\/]src[\\/]js[\\/]/,
        //       name: 'common',
        //       minChunks: 2,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 60
        //     },
        //     styles: {
        //       name: 'styles',
        //       test: /\.(sa|sc|c)ss$/,
        //       chunks: 'all',
        //       enforce: true
        //     },
        //     runtimeChunk: {
        //       name: 'manifest'
        //     }
        //   }
        // },
        minimizer: [
          new TerserPlugin({
            parallel: Math.max(2, require('os').cpus().length - 1),
            terserOptions: {
              format: {
                comments: false
              },
              compress: {
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ['console.log', 'console.warn', 'console.error'], //去除console
                passes: 1, // 减少压缩轮数以提升速度
              },
              mangle: {
                safari10: true,
                // 保留 Reflect API 相关的标识符
                reserved: ['Reflect', 'ownKeys', 'getOwnPropertyDescriptor', 'defineProperty'],
                // 保留对象属性名不被混淆
                properties: false
              },
            },
            extractComments: false,
          })
        ]
      };
      // 优化模块解析 - 简化配置避免冲突
      config.resolve = {
        ...config.resolve,
        // 优化符号链接
        symlinks: false,
        // 缓存模块解析
        cache: true,
      };

      Object.assign(config, {
        optimization
      });
    } else {
      config.mode = 'development';
      // webpack 5 开发环境缓存配置
      config.cache = {
        type: 'filesystem',
        cacheDirectory: path.join(__dirname, './node_modules/.cache/webpack-dev'),
      };
    }
  },
  // module: {
  //   rules: {
  //     test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
  //     loader: 'url-loader',
  //     options: {
  //       limit: 10000,
  //       name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
  //     }
  //   }
  // },
  // 设为FALSE打包时不生成.MAP文件
  productionSourceMap: process.env.VUE_APP_NODE_ENV === 'production' && process.env.VUE_APP_CMD === 'build',
  transpileDependencies: ['date-week-range', 'element-ui'],

  // 并行处理
  parallel: require('os').cpus().length > 1,

  // CSS相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: process.env.NODE_ENV === 'production',
    // 开启 CSS source maps?
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {
      // 给 less-loader 传递 Less.js 相关选项
      less: {
        // 若使用 less-loader@5，请移除 lessOptions 这一级直接配置选项。
        lessOptions: {
          modifyVars: {
            // 直接覆盖变量
            'text-color': '#111',
            'border-color': '#eee',
          },
          javascriptEnabled: true,
        },
      },
    },
  },
  // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
  devServer: {
    // proxy: 'localhost:3000'
    port: 3000,
    hot: true,
    open: true,
    // webpack 5 替换 disableHostCheck 为 allowedHosts
    allowedHosts: 'all',
    // proxy: {
    //   '/api': {
    //     target: '0.0.0.3000',
    //     changeOrigin: true,
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }
  }
};
