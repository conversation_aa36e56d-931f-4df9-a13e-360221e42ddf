import customDatePicker from '../components/CustomDatePicker/CustomDatePicker';
import showEcharts from '@/mixins/showEcharts';
import { number_format } from '@/libs/filters';
export default {
  mixins: [showEcharts],
  data() {
    return {
      formDate: [],
      date_type: '', // 获取数据后的确认的状态
      _date_type: '', // 实时状态
      custom_lastText: '',
    };
  },
  components: {
    customDatePicker,
  },
  watch: {
    formDate: {
      handler(val) {
        if (val && val[0]) {
          this.setDateType();
          this.dateChange();
          this.custom_lastText = this.__lastText;
        }
      },
    },
  },
  computed: {
    __lastText() {
      switch (this.date_type) {
        case 'day':
          return '前一日';
        case 'week':
          return '前一周';
        case 'month':
          return '前一月';
        default:
          return '';
      }
    },
  },
  methods: {
    // 统计时间
    getDateType(getDateType) {
      this._date_type = getDateType;
    },
    setDateType() {
      if (this._date_type !== 'daterange') {
        this.date_type = this._date_type;
      } else {
        this.date_type = '';
      }
    },
    // 获取趋势图
    getTrend(cardList, currentIndex) {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
        item: cardList[currentIndex].value,
      };
      this.$api.getTrend(params).then(res => {
        const xData = res.list.map(item => item.x);
        const yData = res.list.map(item => item.y);

        this.line_options = this.$eChartFn.areaLineOptions(
          {
            xAxis: {
              data: xData,
            },
            series: [{ data: yData }],
          },
          cardList[currentIndex].label,
          cardList[currentIndex].isMoney || false
        );
      });
    },

    // echarts配置
    /**
     * @description: 饼图配置
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * isShow: 是否显示toolTip, 默认显示
     * */

    setPieConfigure(value, data, title, isShow = true) {
      data.map(item => {
        item.name = item.title || item.text || item.age_text;
      });
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption',
        },
        series: [
          {
            data,
            name: 'consumption',
          },
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: isShow,
        },
      });
    },

    /**
     * @description: 饼图配置, 默认配置引导线展示
     * value: 配置函数模板
     * data: 数据源
     * name: '', // 名称
     * rate: '', // 比例
     * value: '', // 参与默认计算的字段
     * */

    setPieConfigure_guide(value, data, title) {
      data.map(item => {
        item.name = item.title || item.text || item.name;
      });
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: -5,
          left: 'center',
          name: 'consumption',
        },
        series: [
          {
            data,
            minAngle: 10,
            name: '',
            radius: [15, '45%'],
            label: {
              alignTo: 'edge', // 文字对齐，文字的边距由 label.edgeDistance 决定。
              edgeDistance: '10%',
              minMargin: 10,
              show: true,
              overflow: 'truncate',
              ellipsis: '...',
              lineHeight: 15,
              formatter: function (e) {
                let data = e.data;
                return `{a|${data.name}: }\n{b|${data.value}}{c| (${data.rate}%)}`;
              },
              rich: {
                a: {
                  // color: '#333333',
                  color: '#A7AFC0',
                  fontSize: 11,
                  lineHeight: 16,
                  fontWeight: '400',
                  align: 'none',
                },
                b: {
                  fontSize: 11,
                  lineHeight: 16,
                  align: 'none',
                  fontWeight: 300,
                  color: '#A7AFC0',
                },
                c: {
                  fontSize: 11,
                  lineHeight: 16,
                  align: 'none ',
                  fontWeight: 300,
                  color: '#A7AFC0',
                },
              },
            },
            labelLine: {
              show: true, //引导线显示
              length: 15,
              length2: 0,
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < 260;
              // const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points,
              };
            },
          },
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: data[0]?.tooltip?.length,
        },
      });
    },

    /**
     * @description: 处理双柱状图+折线
     * value: 配置函数模板
     * data: 数据源
     * {
     *  date: [],
     *  legend: [],
     *  xData: [],
     *  yData: [],
     *  zData: [],
     *  value_text: 柱体上方显示的文本
     *  tooltip_text: tooltip中数值显示的文本
     *}
     * */
    handleBarLine(value, data) {
      this[value] = this.$eChartFn.verticalBar({
        grid: {
          top: '40',
          left: '60',
          right: '40',
          bottom: '80',
          show: true,
        },
        legend: {
          top: '0',
          right: '0',
          show: true,
          data: data?.legend,
        },
        tooltip: {
          show: true, // 开启悬浮框
          trigger: 'axis',
          position: function (point, params, dom, rect, size) {
            // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
            var x = point[0]; //
            var y = point[1];
            var viewWidth = size.viewSize[0];
            var viewHeight = size.viewSize[1];
            var boxWidth = size.contentSize[0];
            var boxHeight = size.contentSize[1];
            var posX = 0; // x坐标位置
            var posY = 0; // y坐标位置
            if (x < boxWidth) {
              // 左边放不开
              posX = 5;
            } else {
              // 左边放的下
              posX = x - boxWidth;
            }

            if (y < boxHeight) {
              // 上边放不开
              posY = 5;
            } else {
              // 上边放得下
              posY = y - boxHeight - 25;
            }

            return [posX, posY];
          },
          formatter: (a, index) => {
            let content = `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${a[0]?.axisValueLabel}</span><br>`;
            a?.forEach((item, index) => {
              content =
                content +
                `
                  <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${
                    item.color
                  }"></span>
                  <span style="font-size: 13px;line-height: 15px;">${item.seriesName}</span>
                  <span style="font-size: 13px;line-height: 15px;font-weight: bold">${
                    item?.data?.tooltip_text ? `${item.data.tooltip_text}` : item.value
                  }</span></br>`;
            });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          padding: 10, // 边框距离内容的距离
          left: 'center',
          backgroundColor: '#fff', // 边框色
          borderRadius: 4,
        },
        dataZoom: [
          {
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 12,
            height: 20,
            left: 80,
            right: 80,
          },
        ],
        xAxis: {
          data: data.date,
          axisLabel: {
            formatter: '{value}',
          },
        },
        yAxis: [
          {
            name: '',
            axisLine: {
              show: false,
              lineStyle: {
                color: '#999',
              },
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              formatter: function (value, index) {
                if (data.leftSymbol == '%') {
                  return `${value}${data.leftSymbol}`;
                } else {
                  return `¥${value}`;
                }
              },
            },
          },
          {
            type: 'value',
            name: '',
            // min: 0,
            // max: 100,
            // interval: 5,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FCC324',
              },
            },
            axisLabel: {
              formatter: '{value}%',
            },
          },
        ],
        series: [
          {
            data: data.xData,
            name: data?.legend[0],
            type: 'bar',
            color: '#3184F1', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              textStyle: {
                color: '#3184F1',
              },
              formatter: function (value, index) {
                let value_text = value?.data?.value_text;
                return value_text ? value_text : value.value;
              },
            },
          },
          {
            data: data.yData,
            name: data?.legend[1],
            type: 'bar',
            color: '#4DCC6E', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              show: true,
              formatter: function (value, index) {
                let value_text = value?.data?.value_text;
                return value_text ? value_text : value.value;
              },
              position: 'top',
              textStyle: {
                color: '#4DCC6E',
                fontSize: 14,
              },
            },
          },
          {
            data: data.zData,
            name: data?.legend[2],
            yAxisIndex: 1,
            type: 'line',
            color: '#FCC324', // 更改柱体的颜色
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#FCC324',
              },
              formatter: function (value, index) {
                return `${value.value}%`;
              },
            },
          },
        ],
      });
    },

    /**
     * @description: 处理单柱状图, 展示横竖柱，柱体显示数字
     * value: 配置函数模板
     * data: 数据源
     * */
    handleBar(value, data) {
      // 为每日利润图表自定义tooltip
      const customTooltip =
        value === 'profit_options'
          ? {
              show: true,
              trigger: 'axis',
              position: function (point, params, dom, rect, size) {
                var x = point[0];
                var y = point[1];
                var viewWidth = size.viewSize[0];
                var viewHeight = size.viewSize[1];
                var boxWidth = size.contentSize[0];
                var boxHeight = size.contentSize[1];
                var posX = 0;
                var posY = 0;
                if (x < boxWidth) {
                  posX = 5;
                } else {
                  posX = x - boxWidth;
                }
                if (y < boxHeight) {
                  posY = 5;
                } else {
                  posY = y - boxHeight - 25;
                }
                return [posX, posY];
              },
              formatter: params => {
                const param = params[0];
                const data = param.data;

                // 构建表格内容，实现两端对齐
                let tableRows = '';

                // 利润行（主要数据，加粗显示）
                tableRows += `
                  <tr>
                    <td style="padding: 2px 0; font-size: 13px; line-height: 15px; color: #333333;">
                      <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: #FC8333;margin-right: 5px;"></span>利润
                    </td>
                    <td style="padding: 2px 0; font-size: 13px; line-height: 15px; font-weight: bold; color: #333333; text-align: right;">
                      ¥${number_format(data.value || 0, 2)}
                    </td>
                  </tr>
                `;

                // 营收
                if (data.confirm_revenue !== undefined) {
                  tableRows += `
                    <tr>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px;">营收</td>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px; text-align: right;">
                        ¥${number_format(data.confirm_revenue || 0, 2)}
                      </td>
                    </tr>
                  `;
                }

                // 固定成本
                if (data.base_cost_amount !== undefined) {
                  tableRows += `
                    <tr>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px;">固定成本</td>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px; text-align: right;">
                        ¥${number_format(data.base_cost_amount || 0, 2)}
                      </td>
                    </tr>
                  `;
                }

                // 订单关联物料成本
                if (data.order_stock_cost_amount !== undefined) {
                  tableRows += `
                    <tr>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px;">订单关联物料成本</td>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px; text-align: right;">
                        ¥${number_format(data.order_stock_cost_amount || 0, 2)}
                      </td>
                    </tr>
                  `;
                }

                // 运营费用成本
                if (data.opex_cost_amount !== undefined) {
                  tableRows += `
                    <tr>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px;">运营费用成本</td>
                      <td style="padding: 2px 0; font-size: 11px; font-weight: 300; color: #A7AFC0; line-height: 15px; text-align: right;">
                        ¥${number_format(data.opex_cost_amount || 0, 2)}
                      </td>
                    </tr>
                  `;
                }

                return `
                  <div>
                    <div style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;margin-bottom: 5px;">${param.axisValueLabel}</div>
                    <table style="width: 100%; border-collapse: collapse;">
                      ${tableRows}
                    </table>
                  </div>
                `;
              },
              padding: 10,
              backgroundColor: '#fff',
              borderRadius: 4,
            }
          : {
              show: true, // 开启悬浮框
            };

      this[value] = this.$eChartFn.verticalBar({
        grid: {
          top: '40',
          left: '60',
          right: '40',
          bottom: '80',
          show: true,
        },
        legend: {
          top: '0',
          right: '0',
          show: true,
          data: data.legend,
        },
        tooltip: customTooltip,
        dataZoom: [
          {
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 12,
            height: 20,
            left: 80,
            right: 80,
          },
        ],
        xAxis: {
          data: data.date,
        },
        yAxis: {
          name: '',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#999',
            },
          },
          // splitLine: {
          //   show: true, // 去除y轴的网格线
          //   lineStyle: {
          //     type: 'dashed',
          //   },
          // },
          axisLabel: {
            formatter: '¥{value}',
          },
        },
        series: [
          {
            data: data.xData,
            name: data.legend[0],
            color: '#FC8333', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              textStyle: {
                color: '#FC8333',
              },
            },
          },
        ],
      });
    },
  },
};
