<template>
  <div class="edit-wrapper">
    <Tabs :value="currentTab" :animated="false" @on-click="changeTab">
      <TabPane label="货品详情" name="product">
        <Form ref="prodForm" :label-width="100" :model="formData" :rules="ruleValidate" style="margin-bottom: 20px">
          <div>
            <div class="title-block flex flex-item-align flex-item-between">
              <div>基础信息</div>
              <Button type="default" class="create-btn" @click="createEvent" v-if="!isDetail">
                <div class="flex flex-item-align">
                  <img class="add-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1022/134446_68016.png" />
                  <span>从模板导入创建</span>
                </div>
              </Button>
            </div>

            <Row>
              <Col span="8">
                <FormItem label="类型" prop="prod_type">
                  <Select
                    v-model="formData.prod_type"
                    placeholder="选择类型"
                    @on-select="onChangeProdType"
                    :disabled="isDetail || isFromTemplate"
                  >
                    <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="通用名" prop="generic_name">
                  <common-name-search
                    ref="nameSearch"
                    :prod_type="formData.prod_type"
                    v-model="formData.generic_name"
                    @getZY="zy => (formData.phonetic_code = zy)"
                    @getItem="getSearchItem"
                    :disabled="isFromTemplate || is_system || isGenericNameDisabled"
                  ></common-name-search>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="品级" prop="grade" v-if="formData.prod_type == '2'">
                  <Select
                    v-model="formData.grade"
                    :placeholder="isFromTemplate || is_system ? '' : '选择品级'"
                    clearable
                    :disabled="isFromTemplate || is_system"
                  >
                    <Option v-for="(item, index) in gradeDesc" :key="index" :value="item.id">{{ item.desc }}</Option>
                  </Select>
                </FormItem>

                <FormItem
                  label="商品名"
                  prop="prod_name"
                  v-if="formData.prod_type === '1' || formData.prod_type === '3'"
                >
                  <Input v-model="formData.prod_name" placeholder="商品名"></Input>
                </FormItem>
              </Col>
            </Row>

            <Row>
              <Col span="8">
                <FormItem :label="is_rst_opc ? '生产厂家' : '产地/厂家'" prop="manufacturer">
                  <Input
                    v-model="formData.manufacturer"
                    :placeholder="isFromTemplate || is_system ? '' : '产地/厂家'"
                    :disabled="isFromTemplate || is_system"
                  ></Input>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="AiCode" prop="is_related">
                  <Input v-model="is_related" placeholder="未关联" disabled></Input>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="助记码" prop="phonetic_code">
                  <Input v-model="formData.phonetic_code" placeholder="助记码" disabled></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8" v-if="isShowApproval_number || is_rst_opc">
                <FormItem label="批准文号" prop="approval_number">
                  <Input v-model="formData.approval_number" placeholder="批准文号"></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="is_rst_opc && [17].includes(+formData.prod_type)">
                <FormItem label="国产/进口" prop="product_origin">
                  <Select v-model="formData.product_origin" :placeholder="is_system ? '' : '请选择'">
                    <Option v-for="item in originDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="8" v-if="is_rst_opc && [17].includes(+formData.prod_type)">
                <FormItem label="注册证号" prop="registration_number">
                  <Input
                    v-model="formData.registration_number"
                    :placeholder="is_system ? '' : '注册证号'"
                    :disabled="is_system"
                  ></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="is_rst_opc && [17].includes(+formData.prod_type)">
                <FormItem label="注册证名称" prop="registration_name">
                  <Input
                    v-model="formData.registration_name"
                    :placeholder="is_system ? '' : '注册证名称'"
                    :disabled="is_system"
                  ></Input>
                </FormItem>
              </Col>
              <Col span="8" v-if="is_rst_opc && [17].includes(+formData.prod_type)">
                <FormItem label="供应商" prop="supplier_name">
                  <Input
                    v-model="formData.supplier_name"
                    :placeholder="is_system ? '' : '供应商'"
                    :disabled="is_system"
                  ></Input>
                </FormItem>
              </Col>

              <Col span="8" v-if="is_rst_opc && [2, 3, 15, 16].includes(+formData.prod_type)">
                <FormItem label="剂型" prop="dosage_form">
                  <Input
                    v-model="formData.dosage_form"
                    :placeholder="is_system ? '' : '剂型'"
                    :disabled="is_system"
                  ></Input>
                </FormItem>
              </Col>

              <Col span="8" v-if="is_rst_opc && [2, 3, 15, 16].includes(+formData.prod_type)">
                <FormItem label="剂量" prop="dosage_amount">
                  <Input
                    v-model="formData.dosage_amount"
                    :placeholder="is_system ? '' : '剂量'"
                    :disabled="is_system"
                  ></Input>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="条形码" prop="bar_code">
                  <Input v-model="formData.bar_code" placeholder="条形码"></Input>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem label="柜号" prop="container_number">
                  <Input v-model="formData.container_number" placeholder="柜号"></Input>
                  <div v-if="formData.prod_type === '2'" class="cabinet-tip">中药存取柜号，如B-3-6</div>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="line"></div>
          <div>
            <div class="title-block">规格信息</div>

            <Row>
              <Col span="8">
                <FormItem :label="is_rst_opc ? '包装' : '规格'" prop="prod_spec">
                  <Input
                    v-model="formData.prod_spec"
                    :placeholder="isFromTemplate || is_system ? '' : is_rst_opc ? '包装' : '规格'"
                    :disabled="isFromTemplate || is_system"
                  ></Input>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem :label="is_rst_opc ? '包装单位' : '零售单位'" prop="prod_unit">
                  <Select
                    v-model="formData.prod_unit"
                    filterable
                    :placeholder="is_rst_opc ? '包装单位' : '零售单位'"
                    :disabled="isFromTemplate || is_system"
                  >
                    <Option
                      v-for="(desc, key) in prodUnits"
                      :disabled="formData.is_split === '2' ? false : desc == formData.split_prod_unit"
                      :key="key"
                      :value="desc"
                      :label="desc"
                    ></Option>
                  </Select>
                </FormItem>
              </Col>

              <Col span="8">
                <FormItem :label="is_rst_opc ? '单价' : '零售价'" prop="retail_price">
                  <InputNumber
                    v-model="formData.retail_price"
                    :active-change="false"
                    :min="0"
                    :precision="2"
                    @on-change="changeRetailPrice"
                    placeholder="零售价"
                    style="width: 100%"
                  ></InputNumber>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="是否拆零" prop="is_split" @on-change="changeSplit">
                  <RadioGroup v-model="formData.is_split">
                    <Radio label="1" style="width: 60px" :disabled="isDetail || isFromTemplate">是</Radio>
                    <Radio label="2" style="width: 60px" :disabled="isDetail || isFromTemplate">否</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>

              <Col v-if="formData.is_split == '1' && !is_rst_opc" span="8">
                <FormItem label="拆零比" prop="split_num">
                  <Input
                    v-model="formData.split_num"
                    @input.native="changeNum"
                    placeholder="拆零比"
                    @on-change="changeSplitNum"
                    :disabled="isDetail || isFromTemplate"
                  >
                    <span slot="append">
                      <Select
                        v-model="formData.split_prod_unit"
                        filterable
                        placeholder="拆零单位"
                        :disabled="isDetail || isFromTemplate"
                        style="width: 94px"
                      >
                        <Option
                          v-for="(desc, key) in prodUnits"
                          :key="key"
                          :disabled="desc == formData.prod_unit"
                          :value="desc"
                          :label="desc"
                        ></Option>
                      </Select>
                    </span>
                  </Input>
                </FormItem>
              </Col>
              <Col v-if="formData.is_split == '1' && is_rst_opc" span="8">
                <FormItem label="拆零比" prop="split_num">
                  <Input
                    v-model="formData.split_num"
                    @input.native="changeNum"
                    placeholder="拆零比"
                    @on-change="changeSplitNum"
                    :disabled="isDetail || isFromTemplate"
                  />
                </FormItem>
              </Col>
              <Col v-if="formData.is_split == '1' && is_rst_opc" span="8">
                <FormItem label="最小计价单位" prop="split_prod_unit">
                  <Select
                    v-model="formData.split_prod_unit"
                    filterable
                    placeholder="最小计价单位"
                    :disabled="isDetail || isFromTemplate"
                    style="width: 100%"
                  >
                    <Option
                      v-for="(desc, key) in prodUnits"
                      :key="key"
                      :disabled="desc == formData.prod_unit"
                      :value="desc"
                      :label="desc"
                    ></Option>
                  </Select>
                </FormItem>
              </Col>

              <Col v-if="formData.is_split == '1'" span="8">
                <FormItem label="拆零价" prop="split_price">
                  <InputNumber
                    v-model="formData.split_price"
                    :active-change="false"
                    :min="0"
                    :precision="4"
                    placeholder="拆零价"
                    style="width: 100%"
                  ></InputNumber>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="line"></div>
          <div>
            <div class="title-block">附加信息</div>
            <Row>
              <Col span="8">
                <FormItem label="预警数量" prop="stock_warning">
                  <div class="flex" style="width: 100%">
                    <InputNumber style="flex: 1" placeholder="预警数量" :min="0" v-model="formData.stock_warning">
                    </InputNumber>
                    <span class="number-box">{{ getWarningUnit || '单位' }}</span>
                  </div>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24">
                <FormItem label="货品图片" prop="instructions_imgs">
                  <div v-if="(isFromTemplate || is_system) && formData.instructions_imgs?.length === 0">-</div>
                  <MaterialPicture
                    v-else
                    v-model="formData.instructions_imgs"
                    :limit="9"
                    :disabled="isFromTemplate || is_system"
                  />
                  <div class="suggest-tip">
                    建议尺寸：800*800像素，图片大小不超过3M，最多上传9张，你可以拖拽图片调整顺序
                  </div>
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </TabPane>
      <TabPane label="批次详情" name="batch" v-if="$route.query.id">
        <Form ref="batchForm" :model="batchForm" inline @submit.native.prevent @keyup.enter.native="onSearchBatch">
          <FormItem>
            <Select v-model="batchForm.supplier_id" placeholder="全部供应商">
              <Option value="">{{ '全部供应商' }}</Option>
              <Option v-for="item in supplierTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <DatePicker
              type="daterange"
              placeholder="入库时间"
              :value="instockTimeRange"
              clearable
              @on-change="times => handleTimeChange(times, 'create_time_st', 'create_time_et')"
            ></DatePicker>
          </FormItem>
          <FormItem>
            <DatePicker
              type="daterange"
              placeholder="生产日期"
              :value="productionTimeRange"
              clearable
              @on-change="times => handleTimeChange(times, 'produce_time_st', 'produce_time_et')"
            ></DatePicker>
          </FormItem>
          <FormItem>
            <DatePicker
              type="daterange"
              placeholder="有效日期"
              :value="expireTimeRange"
              clearable
              @on-change="times => handleTimeChange(times, 'expire_time_st', 'expire_time_et')"
            ></DatePicker>
          </FormItem>
          <FormItem>
            <Button type="primary" class="mr10" @click="onSearchBatch">筛选</Button>
            <Button class="mr10" @click="exportExcel">导出</Button>
            <span class="list-reset-btn" @click="initSearch"
              ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
            >
          </FormItem>
        </Form>
        <Table
          :columns="tableCols"
          :data="batchList"
          :height="$store.state.app.clientHeight - 310"
          class="mb20"
          @on-sort-change="sortChanged"
          :row-class-name="disableClassName"
        >
          <template v-slot:produce_time="{ row }">
            {{ row.produce_time | data_format('YYYY-MM-DD') }}
          </template>
          <template v-slot:expire_time="{ row }">
            {{ row.expire_time | data_format('YYYY-MM-DD') }}
          </template>
          <template v-slot:create_time="{ row }">
            {{ row.create_time | data_format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-slot:bill_code="{ row }">
            {{ row.bill_code || '-' }}
          </template>
          <template v-slot:action="{ row }">
            <a @click="showBatchDetail(row)">详情</a>
          </template>
        </Table>
        <div class="global-list-page" ref="pageRefs">
          <KPage
            :total="batchTotal"
            :page-size.sync="batchForm.pageSize"
            :current.sync="batchForm.page"
            @on-change="onBatchPageChange"
            style="text-align: right"
          />
        </div>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <Button :loading="saveBtnLoading" type="primary" @click="onSave">保存</Button>
      <Dvd />
      <Button @click="onReset" v-if="!isDetail">重置</Button>
      <Dvd />
      <back-button></back-button>
    </div>

    <!-- 模板导入创建 -->
    <template-create v-model="templateVisible" @getItem="getTemplateItem"></template-create>

    <batch-detail-modal
      :visible.sync="batchVisible"
      :batch-info="batchInfo"
      :prod-type="formData.prod_type"
    ></batch-detail-modal>
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import downloadExcel from '@/mixins/downloadExcel';
import { $operator } from '@/libs/operation';
import templateCreate from './components/templateCreate.vue';
import commonNameSearch from './components/commonNameSearch.vue';
import batchDetailModal from './components/batchDetailModal.vue';
import { isRstClinic, isRstOpcClinic } from '@/libs/runtime';

let init_form_data = {
  prod_type: '', // 类型
  generic_name: '', // 通用名
  prod_name: '', // 商品名
  grade: '', // 品级
  manufacturer: '', // 产地/厂家
  phonetic_code: '', // 助记码
  approval_number: '', // 批准文号
  dosage_form: '', // 剂型
  dosage_amount: '', // 剂量
  bar_code: '', // 条形码
  container_number: '', // 柜号
  packaging: '', // 包装
  product_origin: '', // 国产/进口
  registration_number: '', // 注册证号
  registration_name: '', // 注册证名称
  supplier: '', // 供应商
  prod_spec: '', // 规格
  prod_unit: '', // 零售单位
  retail_price: null, // 零售价格
  is_split: '2', // 是否拆零, 默认否
  split_num: '', //  拆零比
  split_prod_unit: '', // 拆零比unit
  split_price: null, // 拆零比price

  stock_warning: null, // 预警数量
  instructions_imgs: [], // 货品图片
};

let init_batch_form_data = {
  page: 1,
  pageSize: 20,
  prod_id: '', // 货品id，初始化取路由id
  supplier_id: '',
  create_time_st: '',
  create_time_et: '',
  expire_time_st: '',
  expire_time_et: '',
  produce_time_st: '',
  produce_time_et: '',
};
export default {
  name: 'edit',
  components: { templateCreate, commonNameSearch, batchDetailModal },
  mixins: [downloadExcel],
  data() {
    return {
      formData: { ...init_form_data },
      ruleValidate: {
        prod_type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        generic_name: [{ required: true, message: '请填写通用名', trigger: 'change' }],
        prod_unit: [{ required: true, message: '请选择零售单位', trigger: 'change' }],
        split_prod_unit: [{ required: true, message: '请选择最小计价单位', trigger: 'change' }],
        retail_price: [{ required: true, type: 'number', min: 0, message: '请填写零售价', trigger: 'change' }],
        split_num: [{ required: true, message: '请填写拆零比', trigger: 'change' }],
        split_price: [{ required: true, type: 'number', message: '请填写拆零价', trigger: 'change' }],
      },
      prodTypes: [], // 类型
      gradeDesc: [], // 品级
      originDesc: [], // 国产/进口
      prodUnits: {}, // 零售单位
      is_related: '未关联', // 关联aicode，有值显示已关联，无值显示未关联
      ai_prod_code: '',

      saveBtnLoading: false, // 保存
      templateVisible: false,
      isFromTemplate: false, // 是否从模板导入
      pms_lp_id: '',
      source: '',
      detail_info: {},
      currentTab: '',
      tableCols: [
        { title: '批次编码', key: 'id', align: 'center', width: 80 },
        { title: '生产批号', key: 'batch_code', align: 'center' },
        { title: '当前库存', key: 'stock_num', align: 'center' },
        { title: '生产日期', slot: 'produce_time', align: 'center', sortable: 'custom' },
        { title: '有效期', slot: 'expire_time', align: 'center', sortable: 'custom' },
        { title: '供应商', key: 'supplier_name', align: 'center' },
        { title: '入库方式', key: 'type_text', align: 'center' },
        { title: '入库时间', slot: 'create_time', align: 'center', sortable: 'custom', width: 160 },
        { title: '入库单号', slot: 'bill_code', align: 'center', width: 100 },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      batchList: [{ a: 1 }],
      batchForm: { ...init_batch_form_data },
      supplierTypes: [],
      timeRange: [],
      instockTimeRange: [],
      productionTimeRange: [],
      expireTimeRange: [],
      batchTotal: 0,
      batchVisible: false,
      batchInfo: {},
      supplierList: [],
    };
  },
  computed: {
    is_rst_opc() {
      return isRstOpcClinic();
    },
    // 自建中药饮片，且关联aicode，通用名不允许修改
    isGenericNameDisabled() {
      if (this.formData.prod_type === '2' && this.ai_prod_code && !!this.$route.query.id) {
        return true;
      } else {
        return false;
      }
    },
    // 是否是系统内建
    is_system() {
      return this.source === '9';
    },
    /**
     * 是否显示批准文号
     * */
    isShowApproval_number() {
      let type = this.formData.prod_type;
      switch (type) {
        case '2': // 中药饮片
        case '15': // 贵细药材
        case '16': // 中药饮片（非处方）
          return false;
        default:
          return true;
      }
    },
    // 获取预警单位
    getWarningUnit() {
      if (this.formData.is_split == '1') {
        return this.formData.split_prod_unit;
      } else {
        return this.formData.prod_unit;
      }
    },
    // 是否详情
    isDetail() {
      return !!this.$route.query.id;
    },
  },
  watch: {},
  created() {
    this.getInStockProductOptions();
    this.init();
    this.getSupplierPullDownList();
    if (this.$route.query.id) {
      this.getProductInfo();
      init_batch_form_data.prod_id = this.$route.query.id;
      this.batchForm.prod_id = this.$route.query.id;
      this.getProdBatchList();
    }
    if (this.is_rst_opc) {
      this.handleQuerySupplierList();
    }
  },
  mounted() {},
  methods: {
    init() {
      this.formData.prod_type = '2';
      this.onChangeProdType({ value: '2' });
    },
    handleQuerySupplierList() {
      this.$api
        .getSupplierList({
          page: 1,
          pageSize: 9999,
        })
        .then(res => {
          this.supplierList = res?.suppliers || [];
        });
    },
    getSearchItem(item = {}) {
      this.is_related = item.ai_code ? '已关联' : '未关联';
      this.ai_prod_code = item.ai_code;
    },
    getTemplateItem(item) {
      this.isFromTemplate = true;
      this.pms_lp_id = item.id;
      this.$refs.prodForm.resetFields();
      this.handlerEcho(item);
    },

    // 类型变更
    onChangeProdType(prod_type) {
      if (prod_type.value === '2') {
        this.formData.is_split = '1';
      } else {
        this.formData.is_split = '2';
      }
    },

    createEvent() {
      this.templateVisible = true;
    },

    validFields() {
      if (this.formData.is_split === '1' && !this.formData.split_prod_unit) {
        this.$Message.error('拆零单位不能为空');
        return false;
      }
      return true;
    },

    getProductEdit() {
      let params = { ...this.formData, id: this.$route.query.id, pms_lp_id: this.pms_lp_id };
      this.saveBtnLoading = true;
      this.$api
        .getProductEdit(params)
        .then(() => {
          this.$Message.success('保存成功');
          this.$router.push('/stock/product/list');
        })
        .finally(() => (this.saveBtnLoading = false));
    },

    onSave() {
      this.$refs['prodForm'].validate(valid => {
        if (valid && this.validFields()) {
          if (this.formData.prod_type === '2' && this.ai_prod_code) {
            if (this.isDetail) {
              // 只有中药饮片详情，且从未关联的数据更改到关联的数据有提示
              let product = this.detail_info?.product || {};
              if (product.ai_prod_code !== this.ai_prod_code && this.source !== '9') {
                this.aiCodeConfirm();
              } else {
                this.getProductEdit();
              }
            } else {
              if (!this.isFromTemplate) {
                this.aiCodeConfirm();
              } else {
                this.getProductEdit();
              }
            }
          } else {
            this.getProductEdit();
          }
        }
      });
    },

    onReset() {
      this.isFromTemplate = false;
      this.is_related = '未关联';
      this.formData.ai_prod_code = '';
      this.$refs.prodForm.resetFields();
      this.init();
    },

    // 零售价
    changeRetailPrice(val) {
      if (!val || !this.formData.split_num) {
        this.formData.split_price = 0;
      } else {
        this.formData.split_price = $operator.divide(this.formData.retail_price, this.formData.split_num, 4);
      }
    },

    // 拆零比
    changeNum(val) {
      if (isNaN(val.target.value)) {
        val.target.value = 0;
      } else {
        val.target.value = Number(val.target.value || 0);
      }
    },

    changeSplitNum(val) {
      if (!val || !this.formData.retail_price) {
        this.formData.split_price = 0;
      } else {
        if (!this.formData.split_num) {
          this.formData.split_price = null;
        } else {
          this.formData.split_price = $operator.divide(this.formData.retail_price, this.formData.split_num, 4);
        }
      }
    },

    changeSplit(val) {
      if (Number(val) !== 1) {
        this.formData.split_prod_unit = '';
      }
    },

    // 获取枚举
    getInStockProductOptions() {
      let params = {};
      this.$api.getInStockProductOptions(params).then(res => {
        this.prodTypes = S.descToArrHandle(res.prodTypes).sort((a, b) => a.sort - b.sort);
        this.gradeDesc = S.descToArrHandle(res.gradeDesc);
        this.originDesc = S.descToArrHandle(res.originDesc);
        this.prodUnits = res.prodUnits;
      });
    },

    // 获取详情
    getProductInfo() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getProductInfo(params).then(res => {
        this.detail_info = res;
        let product = res['product'] || {};
        this.source = product.source || '';
        this.handlerEcho(product);
      });
    },

    handlerEcho(product = {}) {
      // 基础信息
      this.formData.prod_type = product.prod_type;

      this.$refs['nameSearch'].echoInfo([{ name: product.generic_name }]);
      this.formData.generic_name = product.generic_name;
      this.formData.prod_name = product.prod_name;

      this.is_related = product.ai_prod_code ? '已关联' : '未关联';
      this.ai_prod_code = product.ai_prod_code;
      this.formData.phonetic_code = product.phonetic_code;
      this.formData.grade = product.grade;
      this.formData.manufacturer = product.manufacturer;
      this.formData.approval_number = product.approval_number;
      this.formData.product_origin = product.product_origin;
      this.formData.registration_name = product.registration_name;
      this.formData.supplier_name = product.supplier_name;
      this.formData.registration_number = product.registration_number;
      this.formData.dosage_form = product.dosage_form;
      this.formData.dosage_amount = product.dosage_amount;
      this.formData.packaging = product.packaging;
      this.formData.supplier = product.supplier;
      this.formData.bar_code = product.bar_code;
      this.formData.container_number = product.container_number;

      // 规格信息
      this.formData.prod_spec = product.prod_spec;
      this.formData.prod_unit = product.prod_unit;
      this.formData.retail_price = Number(product.retail_price || 0) || null;
      this.formData.is_split = product.is_split;
      this.formData.split_num = product.split_num;
      this.formData.split_prod_unit = product.split_prod_unit;
      this.formData.split_price = Number(product.split_price || 0) || null;

      // 附加信息
      this.formData.stock_warning = product.stock_warning == 0 ? 0 : Number(product.stock_warning || 0) || null;
      this.formData.instructions_imgs = product.instructions_imgs;
    },

    aiCodeConfirm() {
      this.$Modal.confirm({
        title: '是否确认？',
        content: '自建的货品如果绑定了AiCode，后续将无法修改“通用名”',
        okText: '确认保存',
        cancelText: '我再想想',
        onOk: () => {
          this.getProductEdit();
        },
      });
    },
    changeTab() {},
    onSearchBatch() {
      this.batchForm.page = 1;
      this.getProdBatchList();
    },
    exportExcel() {
      this.downloadApiName = 'getProdStockDetailUrl';
      this.downloadExcel(this.batchForm);
    },
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.batchForm[startTime] = times[0];
        this.batchForm[endTime] = times[1];
      } else {
        this.batchForm[startTime] = '';
        this.batchForm[endTime] = '';
      }
    },
    initSearch() {
      this.batchForm = { ...init_batch_form_data };
      this.instockTimeRange = [];
      this.productionTimeRange = [];
      this.expireTimeRange = [];
      this.getProdBatchList();
    },
    onBatchPageChange(page, pageSize) {
      this.batchForm.page = page;
      this.batchForm.pageSize = pageSize;
      this.batchForm.page_size = pageSize;
      this.getProdBatchList();
    },
    sortChanged({ column: { slot }, order }) {
      if (order === 'normal') {
        order = '';
      }

      if (slot) {
        this.batchForm.order = slot + ' ' + order;
        this.getProdBatchList();
      } else {
        this.$Message.error('无效排序字段');
      }
    },
    showBatchDetail(row) {
      this.batchVisible = true;
      this.batchInfo = row;
    },
    getProdBatchList() {
      let params = {
        ...this.batchForm,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getProdBatchList(params)
        .then(res => {
          this.batchTotal = res.total;
          this.batchList = res.list;
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    getSupplierPullDownList() {
      this.$api.getSupplierPullDownList().then(data => {
        this.supplierTypes = data.list;
      });
    },
    disableClassName(row) {
      if (row.stock_num === '0') {
        return 'disableTableRow';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.edit-wrapper {
  .title-block {
    font-weight: 600;
    font-size: 16px;
    color: #303133;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    margin-bottom: 30px;
  }

  .line {
    height: 1px;
    background: #ebedf0;
    margin-top: 24px;
    margin-bottom: 24px;
  }

  .create-btn {
    color: #3088ff;
    border-color: #3088ff;

    .add-icon {
      width: 12px;
      min-width: 12px;
      height: 12px;
      margin-right: 8px;
    }
  }

  .cabinet-tip {
    font-weight: 400;
    font-size: 12px;
    color: #909399;
    line-height: 16px;
    margin-top: 8px;
  }

  .suggest-tip {
    font-weight: 400;
    font-size: 12px;
    color: #909399;
    line-height: 18px;
    margin-top: -10px;
  }
}

.number-box {
  width: 80px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border: 1px solid #bbb;
  border-left: 0;
  background-color: #f8f8f9;
}
</style>
