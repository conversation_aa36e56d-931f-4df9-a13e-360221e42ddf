import Vue from 'vue';
import CommandDialog from './index.vue';

const CommandDialogConstructor = Vue.extend(CommandDialog);

function createDialog(options) {
  const { component, title = '', props = {}, onOk = null, onCancel = null } = options;

  const instance = new CommandDialogConstructor({
    propsData: {
      component,
      title,
      props,
      onOk,
      onCancel,
    },
  });

  instance.$mount();
  document.body.appendChild(instance.$el);
  instance.open();

  return {
    close: () => {
      instance.close();
      // 延迟移除DOM，避免动画问题
      setTimeout(() => {
        document.body.removeChild(instance.$el);
        instance.$destroy();
      }, 300);
    },
  };
}

export default createDialog;
