<template>
  <Modal
    :title="title"
    v-model="visible"
    :closable="false"
    @on-cancel="handleClose"
    @on-visible-change="handleVisibleChange"
  >
    <div v-if="typeof component === 'string' || typeof component === 'number'">
      {{ component }}
    </div>
    <component v-else :is="component" ref="contentComponent" v-bind="props" />
    <span slot="footer" class="dialog-footer">
      <Button @click="handleCancel">取 消</Button>
      <Button type="primary" @click="handleOk" :loading="loading">确 定</Button>
    </span>
  </Modal>
</template>

<script>
export default {
  name: 'CommandDialog',
  props: {
    component: {
      type: [Object, Function, String, Number],
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    props: {
      type: Object,
      default: () => ({}),
    },
    onOk: {
      type: Function,
      default: null,
    },
    onCancel: {
      type: Function,
      default: null,
    },
    onChange: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    async handleOk() {
      try {
        this.loading = true;
        // 如果内容组件有validate方法，先校验
        if (this.$refs.contentComponent && typeof this.$refs.contentComponent.validate === 'function') {
          const valid = await this.$refs.contentComponent.validate();
          this.loading = false;
          if (!valid) return;
        }

        if (this.onOk) {
          const valid = await this.onOk(this.$refs.contentComponent);
          console.log(valid, 'valid')
          this.loading = false;
          if (!valid) return;
        }
        this.loading = false;
        this.close();
      } catch (e) {
        this.loading = false;
        console.error('Dialog onOk error:', e);
      }
    },
    handleCancel() {
      if (this.onCancel) {
        this.onCancel();
      }
      this.close();
    },
    handleClose() {
      this.handleCancel();
    },
    handleVisibleChange(flag) {
      if (typeof this.onChange === 'function') {
        this.onChange(flag);
      }
    },
  },
};
</script>
