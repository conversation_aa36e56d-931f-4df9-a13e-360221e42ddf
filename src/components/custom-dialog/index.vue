<template>
  <Modal
    :value="modelVisible"
    :title="title"
    :mask-closable="false"
    :closable="closable"
    class-name="custom-message-dialog"
    @on-ok="handleOk"
    @on-cancel="handleCancel"
    @on-visible-change="handleVisibleChange"
  >
    <div class="dialog-content" :style="{ textAlign: textAlign }" v-html="content"></div>
    <template #footer>
      <Button v-if="showCancel" @click="handleCancel">{{ cancelText }}</Button>
      <Button type="primary" :loading="loading" @click="handleOk">{{ okText }}</Button>
    </template>
  </Modal>
</template>

<script>
export default {
  name: 'custom-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '提示',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okText: {
      type: String,
      default: '确定',
    },
    textAlign: {
      type: String,
      default: 'center',
    },
    content: {
      type: String,
      default: '',
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    onOk: {
      type: Function,
      default: null,
    },
    onCancel: {
      type: Function,
      default: null,
    },
  },
  computed: {
    modelVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  data() {
    return {
      text: '',
    };
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false);
    },
    handleVisibleChange(val) {
      if (val) {
        this.text = this.content || '';
      } else {
        this.text = '';
      }
    },
    handleOk() {
      if (typeof this.onOk === 'function') {
        try {
          this.onOk()
            .then(() => {
              this.cancel();
            })
            .catch(err => {
              this.cancel();
              console.log(err, 'err');
            });
        } catch (error) {
          this.cancel();
        }
        return;
      }
      this.cancel();
    },
    handleCancel() {
      if (typeof this.onCancel === 'function') {
        this.onCancel().then(() => {
          this.cancel();
        });
        return;
      }
      this.cancel();
    },
  },
};
</script>

<style scoped lang="less">
.dialog-content {
  font-size: 14px;
  padding: 16px 0;
  text-align: center;
}
.custom-message-dialog {

}
</style>
