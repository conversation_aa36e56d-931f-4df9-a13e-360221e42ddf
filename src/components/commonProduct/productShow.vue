<template>
  <div>
    <div class="title flex flex-item-align">
      <div class="custom-k-lint" style="max-width: 180px" v-if="generic_name">
        <k-link :to="{ path: '/stock/product/edit', query: { id: row.prod_id || row.id } }" target="_blank">
          <div @mouseenter="openToolTip" class="link-text">{{ generic_name }}</div>
        </k-link>
      </div>
      <div v-else>-</div>
      <img
        v-if="insure_level_desc"
        src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1220/095550_56832.png"
        class="insure-icon"
      />
    </div>
    <Tooltip placement="bottom-start" :disabled="is_rst" :delay="200" v-if="isShowTooltip">
      <div slot="content">
        <span class="mr4" v-if="grade_desc">{{ grade_desc }}</span>
        <span class="mr4" v-if="prod_spec">{{ prod_spec }}</span>
        <span v-if="manufacturer">{{ manufacturer }}</span>
      </div>
      <div class="subTitle ecs">
        <span class="mr4" v-if="grade_desc">{{ grade_desc }}</span>
        <span class="mr4" v-if="prod_spec">{{ prod_spec }}</span>
        <span v-if="manufacturer">{{ manufacturer }}</span>
      </div>
    </Tooltip>
  </div>
</template>

<script>
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'productShow',
  props: {
    generic_name: {
      type: String,
      default: '',
    },
    prod_spec: {
      type: String,
      default: '',
    },
    grade_desc: {
      type: String,
      default: '',
    },
    manufacturer: {
      type: String,
      default: '',
    },
    insure_level_desc: {
      type: String,
      default: '',
    },
    row: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {},
  mixins: [],
  data() {
    return {};
  },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    isShowTooltip() {
      return this.grade_desc || this.prod_spec || this.manufacturer;
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    openToolTip(e) {
      if (!isRstClinic()) return;
      this.$emit('showPop', 'custom-tooltip', e, this.row);
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  font-weight: 400;
  font-size: 13px;
  color: #323232;
  line-height: 20px;
  text-align: left;
}

.subTitle {
  font-weight: 400;
  font-size: 12px;
  color: #c0c4cc;
  line-height: 14px;
  font-style: normal;
  text-align: left;
  cursor: pointer;
  span {
    white-space: pre-line !important;
  }
}
.mr4 {
  margin-right: 4px;
}
.insure-icon {
  width: 12px;
  height: 12px;
  //vertical-align: text-top;
  margin-left: 3px;
  //padding-bottom: 1px;
  //background-color: #00a0ea;
  //color: #ffffff;
  //padding: 0 1px;
  //border-radius: 2px;
  //font-size: 12px;
}
.custom-k-lint {
  overflow: unset;
  text-overflow: unset;
  white-space: pre-wrap;
  word-break: break-all;
  :deep(a) {
    overflow: unset;
    text-overflow: unset;
    white-space: pre-wrap;
    word-break: break-all;
  }
  :deep(.link-text) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
