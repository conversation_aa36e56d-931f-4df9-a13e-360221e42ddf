<template>
  <div class="wrapper">
    <Modal
      :value="visible"
      title="创建用户"
      :mask-closable="false"
      :before-close="closeModal"
      @on-cancel="closeModal">
      <div class="content">
        <Form ref="formData" :model="formData" :label-width="80" :label-colon="true" :rules="ruleValidate">

          <FormItem label="用户姓名" prop="nickname">
            <Input v-model="formData.nickname" placeholder="请输入用户姓名"></Input>
          </FormItem>

          <FormItem label="用户手机" prop="mobile">
            <Input v-model="formData.mobile" maxlength="11" placeholder="请输入用户手机号"></Input>
          </FormItem>

          <FormItem label="用户性别" prop="sex">
            <Select v-model="formData.sex" placeholder="请选择用户性别">
              <Option :value="sex_item.value" v-for="(sex_item, sex_index) in sexList" :key="sex_index+'sex'">{{ sex_item.label }}</Option>
            </Select>
          </FormItem>

          <FormItem label="出生日期">
            <DatePicker :options="disabledTime" :start-date="new Date('1972-01-01')" type="date" placeholder="请选择出生日期" @on-change="changeDate" :value="formData.birthday"></DatePicker>
          </FormItem>

          <FormItem label="用户来源">
            <Select v-model="formData.source" placeholder="请选择用户来源">
              <Option :value="source_item.id" v-for="(source_item, source_index) in sourceList" :key="source_index+'source'">{{ source_item.desc }}</Option>
            </Select>
          </FormItem>

          <FormItem label="用户等级">
            <Select v-model="formData.offline_level" placeholder="请选择用户等级">
              <Option :value="level" v-for="(level, source_index) in levelList" :key="source_index+'level'">{{ level }}</Option>
            </Select>
          </FormItem>

        </Form>
      </div>
      <div class="footer" slot="footer">
        <Button type="primary" :loading="loading" @click="createNewConsumer">创建新用户</Button>
        <Button  @click="closeModal">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
let init_formData = {
  nickname: '',
  mobile: '',
  birthday: '',
  sex: '',
  source: '',
  offline_level: ''
}
  export default {
    name: "CreateConsumerDia",
    components: {

    },
    mixins: [],
    props: {
      visible: {
        type: Boolean,
        default: () => false
      },
      name: {
        type: String,
        default: () => ''
      },
      levelList:{
        type:Array,
        default: ()=>[]
      },
      sourceList:{
        type:Array,
        default: ()=>[]
      },
      showCreateInfo: {
        type: Function
      }
    },
    data () {
      return {
        disabledTime: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now()
          },
        },
        formData: {...init_formData}, // 创建用户的数据
        loading: false,
        sexList: [
          { label: '男', value: '1' },
          { label: '女', value: '2' },
        ],
        ruleValidate: {
          nickname: [
            { required: true, message: '请输入用户姓名', trigger: 'change' }
          ],
          sex: [
            { required: true, message: '请选择用户性别', trigger: 'change' }
          ],
          mobile: [
            { required: true, message: '请输入用户手机号', trigger: 'change' },
            { trigger: 'change',
              validator: (rule, value, callback) => {
                if (!this.regRole(value)) {
                  callback(new Error('请输入正确的手机号'))
                }else{
                  callback()
                }
              }
            }
          ]
        },
      }
    },
    computed: {

    },
    watch: {
      visible (val) {
        if (!val) {
          this.formData = {...init_formData}
          this.$refs.formData.resetFields()

        }else {
          const reg = /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/
          console.log(reg.test(this.name))
          if(reg.test(this.name)){
            this.formData.mobile = this.name
          }else {
            this.formData.nickname = this.name
          }
        }
      }
    },
    created() {

    },
    mounted() {

    },
    methods: {
      changeDate(date){
        console.log("-> %cdate %o", "font-size: 15px", date)
        this.formData.birthday = date
      },
      // 手机号校验
      regRole(tel){
        let flag;
        let reg = /^1[3456789]\d{9}$/
        flag = reg.test(tel)
        return flag
      },
      closeModal () {
        this.$emit('update:visible', false)
      },
      // 创建新用户
      createNewConsumer () {
        this.$refs['formData'].validate( (valid) => {
          if ( valid ) {
            this.loading = true
            this.$api.createUser(this.formData).then(res=>{
              console.log("-> %cres %o", "font-size: 15px", res)
              this.$Message.success('创建用户成功')
              this.closeModal()
              this.showCreateInfo(res.user)
            },err=>{}).finally(()=>{
              this.loading = false
            })
          }
        } )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
.ivu-date-picker {
  width: 100%;
}
</style>
