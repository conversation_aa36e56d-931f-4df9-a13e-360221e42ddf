<template>
  <Modal
    ref="customModal"
    :value="value"
    width="500px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    :closable="false"
    @on-visible-change="changeVisible"
  >
    <div class="content" v-if="tipType">
      <div class="tip-header">{{ info[tipType].title }}</div>
      <div class="tip-text">
        {{ info[tipType].content_text }}
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">我再想想</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">{{ tipType && info[tipType].btn_text }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'UnableReceiveTipModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    tipType: {
      type: String,
      default: 'temporary'
    }
  },

  data() {
    return {
      confirmLoading: false,
      info: {
        temporary: {
          title: '是否要给你创建一个临时号码？',
          content_text:
            '1. 如果用户没带手机或暂时无法提供验证码，你可以选择给用户分配一个临时号码。\n' +
            '2. 该号码仅限在本店看病、理疗使用，用户未来无法使用该临时号码登录APP、小程序。\n' +
            '3. 后续你可以将用户的临时号码修改为真实手机号。',
          btn_text: '确认使用临时号'
        },
        exist: {
          title: '当前手机号在用户中心已存在',
          content_text:
            '如果一个手机号在用户中心已存在，则无法直接用该手机号创建新用户，但你可以通过同步账号的方式，在本店创建新账号。',
          btn_text: '账号同步到本店'
        }
      }
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
      } else {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$emit('success', this.tipType);
      this.closeModal();
    }
  }
};
</script>

<style scoped lang="less">
.content {
  padding: 30px 20px;
  .tip-header {
    font-weight: 600;
    font-size: 16px;
  }
  .tip-text {
    font-size: 14px;
    margin-top: 20px;
    color: #999;
    white-space: pre-line;
  }
}
::v-deep .ivu-modal-header {
  display: none;
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}
</style>
