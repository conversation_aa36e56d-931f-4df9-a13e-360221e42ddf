<template>
  <Modal
    :modal-append-to-body="true"
    append-to-body
    :title="title"
    width="450"
    custom-class="iframe-dialog"
    height="700"
    :before-close="handleClose"
    :value="visible"
    footer-hide
  >
    <iframe :src="iframeUrl" :width="width" :height="height" ref="diaIframe" />
  </Modal>
</template>

<script>
import * as runtime from '@/libs/runtime';

export default {
  name: 'ReportDetail',
  components: {},
  mixins: [],
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '辨证分析',
    },
    iframeUrl: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '420px',
    },
    height: {
      type: String,
      default: '700px',
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          const iframe = this.$refs.diaIframe;
          iframe.onload = () => {
            // this.onIframeLoaded(iframe)
            window.addEventListener('message', this.handleMessage);
          };
        });
      }
    },
  },

  created() {},

  // mounted() {
  //
  // },

  destroyed() {
    window.removeEventListener('message', this.handleMessage);
  },

  methods: {
    handleClose() {
      this.$emit('changeVisible', false);
    },
    postMsgToIframe() {
      const iframe = this.$refs.diaIframe;
      iframe.contentWindow.postMessage(
        {
          type: 'getXEnv',
          message: {
            plat: 'admin_hospital',
            uid: runtime.getUid(),
          },
        },
        '*'
      );
    },
    handleMessage(e) {
      if (e.data.type === 'webview') {
        if (e.data.message.event === 'checkMore') {
          this.dialogTitle = e.data.message.fullPath.indexOf('tongue') > -1 ? '舌象分析' : '辨证分析';
          this.aiReportUrl = e.data.message.fullPath;
          this.aiDetailVisible = true;
        } else if (e.data.message.event === 'getXEnv') {
          this.postMsgToIframe();
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ivu-modal-fullscreen {
  width: 450px !important;
}
/deep/ .ivu-modal {
  top: 10px;
  height: calc(100vh - 50px);
}
</style>
