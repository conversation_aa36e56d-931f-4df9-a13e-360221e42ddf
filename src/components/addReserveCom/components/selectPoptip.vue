<template>
  <Poptip transfer transfer-class-name="select-poptip" :trigger="trigger" placement="bottom" width="200" ref="pop">
    <div slot="content" class="content">
      <div class="item" v-for="item in 30" @click="handleClick(item)">{{ item }}</div>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
export default {
  name: 'selectPoptip',
  components: {},
  mixins: [],
  props: {
    trigger: {
      type: String,
      default: 'click',
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    handleClick(item) {
      this.$emit('change', item || '');
      this.closePopTip();
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .close-icon {
    cursor: pointer;
    &:hover {
      color: #333 !important;
    }
  }
}
.content {
  height: 200px;
  overflow-y: auto;
  .item {
    list-style-type: none;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    cursor: pointer;
    &:hover {
      background: #f6f4ff;
    }
  }
}
</style>

<style lang="less">
.select-poptip {
  padding: 0px !important;
  .ivu-poptip-arrow {
    display: none;
  }
}
</style>
