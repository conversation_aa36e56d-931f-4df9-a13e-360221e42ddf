<script>
export default {
  name: 'StandardPage',
  data() {
    return {
      tableHeaderHeight: 0,
      tableFooterHeight: 32,
      hasScrollBar: false,
    };
  },
  props: {
    total: {
      type: [Number, String],
      default: 0,
    },
    pageSize: {
      type: [Number, String],
      default: 10,
    },
    current: {
      type: [Number, String],
      default: 1,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    extraHeight: {
      type: [Number, String],
      default: 0,
    },
    height: {
      type: [Number, String],
      default: 0,
    },
    pageSizeOpts: {
      type: Array,
      default() {
        return [10, 20, 50, 80, 100, 200];
      },
    },
  },
  created() {},
  mounted() {
    this.getPageHeight();
    this.observeTableElements();
  },
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.headerObserver) {
      this.headerObserver.disconnect();
    }
    if (this.footerObserver) {
      this.footerObserver.disconnect();
    }
  },
  beforeMount() {},
  computed: {
    isShowHeader() {
      return !!this.$slots.header;
    },
    isShowFooter() {
      return !!this.$slots.footer;
    },
    appInnerHeight() {
      return this.$store.state.app.clientHeight;
    },
    tableHeight() {
      // const paddingH = +(this.paddingTop + this.paddingBottom) || 32;
      const tableHeaderAndFooter = +(this.tableHeaderHeight + this.tableFooterHeight) || 32;
      const extraHeight = +this.extraHeight || 0;
      return this.appInnerHeight - tableHeaderAndFooter - 142 - extraHeight;
    },
  },
  methods: {
    onPageChange(page, pageSize) {
      this.$emit('on-change', page, pageSize);
    },
    getPageHeight() {
      this.$nextTick(() => {
        this.tableHeaderHeight = +this.$refs?.sTableHRef?.clientHeight?.toFixed(2) || 0;
        this.tableFooterHeight = +this.$refs?.sTableFRef?.clientHeight?.toFixed(2) || 32;
        const { clientHeight, scrollHeight } = this.$refs?.sTableRef || {};
        this.$set(this, 'hasScrollBar', scrollHeight > clientHeight);
      });
    },
    observeTableElements() {
      // 创建 ResizeObserver 实例
      this.headerObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
          if (entry.contentRect.height) {
            requestAnimationFrame(() => this.getPageHeight());
          }
        });
      });

      this.footerObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
          if (entry.contentRect.height) {
            requestAnimationFrame(() => this.getPageHeight());
          }
        });
      });

      // 开始观察元素
      this.$nextTick(() => {
        if (this.$refs.sTableHRef) {
          this.headerObserver.observe(this.$refs.sTableHRef);
        }
        if (this.$refs.sTableFRef) {
          this.footerObserver.observe(this.$refs.sTableFRef);
        }
      });
    },
  },
};
</script>

<template>
  <div class="standardTable-wrap">
    <div
      ref="sTableHRef"
      class="standard-table-header"
      :style="{ paddingRight: hasScrollBar ? '10px' : 0 }"
      v-if="isShowHeader"
    >
      <slot name="header"></slot>
    </div>
    <div
      ref="sTableRef"
      class="standard-table"
      :style="{
        height: (height || tableHeight) + 'px',
        paddingRight: hasScrollBar ? '10px' : 0,
      }"
    >
      <slot />
    </div>
    <div ref="sTableFRef" class="standard-table-footer" v-if="showFooter">
      <slot v-if="isShowFooter" name="footer"></slot>
      <template v-else>
        <div class="global-list-page" style="margin-left: auto">
          <KPage
            :total="total"
            :page-size.sync="pageSize"
            :current.sync="current"
            :page-size-opts="pageSizeOpts"
            @on-change="onPageChange"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
.standardTable-wrap {
  overflow: hidden;
}
.standard-table-footer {
  width: 100%;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-top: 16px;
}
.standard-table-header {
  border-top: 1px solid transparent;
  //border-bottom: 1px solid transparent;
}
.standard-table {
  position: relative;
  overflow-y: auto;
}
</style>
