<script>
import { data_format, number_format } from '@/libs/filters';

export default {
  name: 'StandardTable',
  data() {
    return {
      number_format,
      tableHeaderHeight: 0,
      tableFooterHeight: 32,
      // paddingTop: 16,
      // paddingBottom: 16,
      // appInnerHeight: 0,
    };
  },
  props: {
    total: {
      type: [Number, String],
      default: 0,
    },
    pageSize: {
      type: [Number, String],
      default: 10,
    },
    current: {
      type: [Number, String],
      default: 1,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    extraHeight: {
      type: [Number, String],
      default: 0,
    },
    height: {
      type: [Number, String],
      default: 0,
    },
    pageSizeOpts: {
      type: Array,
      default() {
        return [10, 20, 50, 80, 100, 200];
      },
    },
  },
  created() {},
  mounted() {
    this.getPageHeight();
    this.observeTableElements();
  },
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.headerObserver) {
      this.headerObserver.disconnect();
    }
    if (this.footerObserver) {
      this.footerObserver.disconnect();
    }
  },
  updated() {},
  beforeMount() {},
  computed: {
    slotColumns() {
      const flattenSlotColumns = columns => {
        let result = [];

        columns.forEach(column => {
          // 检查当前列是否有slot属性
          if (column.slot) {
            if (column.slot === 'header') {
              throw 'standardTable接收的columns: slot不能存在header插槽';
            }
            result.push(column);
          }

          // 递归处理children
          if (column.children && Array.isArray(column.children)) {
            result = result.concat(flattenSlotColumns(column.children));
          }
        });

        return result;
      };

      return flattenSlotColumns(this.$attrs.columns || []);
    },
    isShowHeader() {
      return !!this.$slots.header;
    },
    isShowFooter() {
      return !!this.$slots.footer;
    },
    appInnerHeight() {
      return this.$store.state.app.clientHeight;
    },
    tableHeight() {
      const tableHeaderAndFooter = +(this.tableHeaderHeight + this.tableFooterHeight) || 32;
      const extraHeight = +this.extraHeight || 0;
      return this.appInnerHeight - tableHeaderAndFooter - 142 - extraHeight;
    },
  },
  methods: {
    data_format,
    onPageChange(page, pageSize) {
      this.$emit('on-change', page, pageSize);
    },
    observeTableElements() {
      // 创建 ResizeObserver 实例
      this.headerObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
          if (entry.contentRect.height) {
            requestAnimationFrame(() => this.getPageHeight());
          }
        });
      });

      this.footerObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
          if (entry.contentRect.height) {
            requestAnimationFrame(() => this.getPageHeight());
          }
        });
      });

      // 开始观察元素
      this.$nextTick(() => {
        if (this.$refs.sTableHRef) {
          this.headerObserver.observe(this.$refs.sTableHRef);
        }
        if (this.$refs.sTableFRef) {
          this.footerObserver.observe(this.$refs.sTableFRef);
        }
      });
    },
    getPageHeight() {
      const headerHeight = this.$refs?.sTableHRef?.clientHeight || 0;
      const footerHeight = this.$refs?.sTableFRef?.clientHeight || 32;

      if (headerHeight !== this.tableHeaderHeight || footerHeight !== this.tableFooterHeight) {
        this.tableHeaderHeight = +headerHeight.toFixed(2);
        this.tableFooterHeight = +footerHeight.toFixed(2);
      }
    },
    handleSort(_index, type) {
      this.$refs.standardTable.handleSort(_index, type);
    },
    selectAll(status) {
      this.$refs.standardTable.selectAll(status);
    },
  },
};
</script>

<template>
  <div class="standardTable-wrap">
    <div ref="sTableHRef" class="standard-table-header" v-if="isShowHeader">
      <slot name="header"></slot>
    </div>
    <div class="standard-table">
      <Table ref="standardTable" v-bind="$attrs" v-on="$listeners" :height="height || tableHeight">
        <template v-for="column in slotColumns" :slot="column.slot" slot-scope="record">
          <template v-if="column.automate">
            <template v-if="column.automate === 'string'">
              <div :key="column.slot" class="text-ellipsis" v-overflow-tooltip="{ placement: 'top' }">
                {{ record?.row?.[column?.slot] || '-' }}
              </div>
            </template>
            <template v-else-if="column.automate === 'date'">
              <div :key="column.slot" class="text-ellipsis" v-overflow-tooltip="{ placement: 'top' }">
                {{
                  +record?.row?.[column?.slot]
                    ? data_format(record?.row?.[column?.slot], column?.automate_format || 'YYYY-MM-DD')
                    : '-'
                }}
              </div>
            </template>
            <template v-else-if="column.automate === 'money'">
              {{
                record?.row?.[column?.slot]
                  ? number_format(record?.row?.[column?.slot], column?.automate_format || 2)
                  : '-'
              }}
            </template>
          </template>
          <slot v-else :name="column.slot ? column.slot : ''" v-bind="record"></slot>
        </template>
      </Table>
      <slot name="emptyData" />
    </div>
    <div ref="sTableFRef" class="standard-table-footer" v-if="showFooter">
      <slot v-if="isShowFooter" name="footer"></slot>
      <template v-else>
        <div class="global-list-page" style="margin-left: auto">
          <KPage
            :total="total"
            :page-size.sync="pageSize"
            :current.sync="current"
            :page-size-opts="pageSizeOpts"
            @on-change="onPageChange"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
.standard-table-footer {
  width: 100%;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-top: 16px;
}
.standard-table-header {
  border-top: 1px solid transparent;
  //border-bottom: 1px solid transparent;
}
.standard-table {
  position: relative;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
