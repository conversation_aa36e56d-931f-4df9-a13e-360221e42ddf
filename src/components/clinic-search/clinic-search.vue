<template>
	<Select
      ref="clinic"
			transfer
			:value="value"
			:clearable="isClearable"
			:loading="searchLoading"
			:remote-method="search"
      :transfer-class-name="className"
      :disabled="disabled"
      filterable
			@on-clear="clearSub"
			@on-query-change="queryChange"
			class="filterable-select"
			:placeholder="placeholder"
      :style="{width:getWidth,height}"
			@on-select="selectSup">
    <Option value="" v-if="showAll">全部诊所</Option>
		<Option v-for="(option, index) in clinic_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select>
	<!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
		<Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
</template>

<script>
import util from '@/libs/util'
export default {
	name: 'clinic-search',
	components: {},
	mixins: [],
	props: {
    disabled: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: Boolean,
      default: true
    },
		isClearable: {
			type: Boolean,
			default: true,
		},
		value: {
			type: String,
			default: ''
		},
    width: {
      type: Number|String,
      default: 200
    },
    height: {
      type: String,
      default: '32px'
    },
    placeholder: {
      type: String,
      default: '请输入搜索诊所'
    },
    className: {
      type: String,
      default: ''
    }
	},
	data() {
		return {
			searchLoading: false,
			// supplierList: [],
			clinic_list: [],
			query: '',
      currentLabel: "cdcff7de-8c9d-4b52-b72f-452089d15dc2"
		}
	},
	computed: {
    getWidth(){
      if(typeof this.width === 'number'){
        return this.width+ 'px'
      }else {
        return this.width
      }
    }
	},
	watch: {},
	created() {
		if (!this.$route.query.clinic_id) {
			this.searchMethod()
		}else{
			let list = JSON.parse(localStorage.getItem('clinic_list')) || []
			this.clinic_list = list
			this.$emit('input', list[0] && list[0].id)
		}
	},
	mounted() {},
	methods: {
		searchMethod: util.debounce(function (query) {
			this.searchLoading = true
			this.$api.getCommonClinicList({name: query,pageSize: 100}).then(res => {
        console.log("-> res", res)
				this.searchLoading = false

				this.clinic_list = res.list
				localStorage.setItem('clinic_list', JSON.stringify(this.clinic_list))
			})
		},200),
		search () {},
		selectSup(val) {
      console.log("-> val", val)
			this.$emit('input', val.value)
      this.$emit('change', val.value)
      this.currentLabel = val.label
		},
		queryChange (val) {
      console.log("-> val", val)
      if(val === this.currentLabel) {
        return
      }
      if(val === '全部诊所'){
        this.searchMethod('')
        return
      }
			this.searchMethod(val)
		},
		clear () {
			this.$refs.clinic.clearSingleSelect()
		},
		clearSub(){
			this.searchMethod()
			this.$emit('input','')
      this.$emit('change', '')
    }
	},
}
</script>

<style lang="less" scoped>
.filterable-select{
	::v-deep .ivu-select-input{
		margin-top: -1px;
	}
}
</style>
