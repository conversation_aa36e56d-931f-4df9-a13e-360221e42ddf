/***
 *  mixins class
 * @param {...Class} mixins
 */
function mixinsClass(...mixins) {
	class MixinClass {
		constructor() {
			for (let mixin of mixins) {
				copyProperties(this, new mixin()); // 拷贝实例属性 同时执行内部初始化
			}
		}
	};
	let proto = MixinClass.prototype;
	for (let mixin of mixins) {
		copyProperties(MixinClass, mixin); // 拷贝静态属性
		copyProperties(proto, mixin.prototype); // 拷贝原型属性
	}
	return MixinClass;
}

function copyProperties(target, source) {
	// 严格的参数检查
	if (!target || (typeof target !== 'object' && typeof target !== 'function')) {
		console.warn('[mixinsClass] copyProperties: invalid target', target);
		return;
	}

	if (!source || (typeof source !== 'object' && typeof source !== 'function')) {
		console.warn('[mixinsClass] copyProperties: invalid source', source);
		return;
	}

	// 安全的获取对象键
	let keys;
	try {
		// 确保 source 是对象
		const sourceObj = Object(source);

		// 使用 Reflect.ownKeys 或降级方案
		if (typeof Reflect !== 'undefined' && typeof Reflect.ownKeys === 'function') {
			keys = Reflect.ownKeys(sourceObj);
		} else {
			// 降级方案
			keys = Object.keys(sourceObj);
			if (typeof Object.getOwnPropertySymbols === 'function') {
				keys = keys.concat(Object.getOwnPropertySymbols(sourceObj));
			}
		}
	} catch (error) {
		console.error('[mixinsClass] Failed to get keys from source:', error, source);
		return;
	}

	// 复制属性
	for (let key of keys) {
		if (key !== 'constructor'
			&& key !== 'prototype'
			&& key !== 'name'
		) {
			try {
				let desc = Object.getOwnPropertyDescriptor(source, key);
				if (desc) {
					Object.defineProperty(target, key, desc);
				}
			} catch (error) {
				// 如果 defineProperty 失败，尝试简单赋值
				try {
					target[key] = source[key];
				} catch (assignError) {
					console.warn('[mixinsClass] Failed to copy property:', key, assignError);
				}
			}
		}
	}
}

export {
	mixinsClass,
}
